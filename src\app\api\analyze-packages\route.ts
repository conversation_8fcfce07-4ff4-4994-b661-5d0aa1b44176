import { NextRequest, NextResponse } from "next/server";
import { PackageManagerService } from "@/lib/package-services";

interface PackageInfo {
  name: string;
  currentVersion: string;
  latestVersion: string;
  status: "up-to-date" | "outdated" | "error";
  packageManager: "npm" | "pip" | "pub";
  description?: string;
  homepage?: string;
}

interface PackageManagerData {
  dependencies: Record<string, string>;
  devDependencies?: Record<string, string>;
  packageManager: "npm" | "pip" | "pub";
}

function parsePackageJson(content: string): PackageManagerData {
  try {
    const parsed = JSON.parse(content);
    const dependencies = parsed.dependencies || {};
    const devDependencies = parsed.devDependencies || {};
    
    return {
      dependencies: { ...dependencies, ...devDependencies },
      packageManager: "npm"
    };
  } catch (error) {
    throw new Error("Invalid JSON format");
  }
}

function parseRequirementsTxt(content: string): PackageManagerData {
  const dependencies: Record<string, string> = {};
  const lines = content.split('\n').filter(line => line.trim() && !line.trim().startsWith('#'));
  
  for (const line of lines) {
    const trimmed = line.trim();
    let name = trimmed;
    let version = "latest";
    
    // Handle different version specifiers
    if (trimmed.includes('==')) {
      [name, version] = trimmed.split('==').map(s => s.trim());
    } else if (trimmed.includes('>=')) {
      [name, version] = trimmed.split('>=').map(s => s.trim());
    } else if (trimmed.includes('<=')) {
      [name, version] = trimmed.split('<=').map(s => s.trim());
    } else if (trimmed.includes('>')) {
      [name, version] = trimmed.split('>').map(s => s.trim());
    } else if (trimmed.includes('<')) {
      [name, version] = trimmed.split('<').map(s => s.trim());
    } else if (trimmed.includes('~=')) {
      [name, version] = trimmed.split('~=').map(s => s.trim());
    }
    
    // Clean package name (remove extras like [extra])
    name = name.split('[')[0].trim();
    
    if (name) {
      dependencies[name] = version;
    }
  }
  
  return {
    dependencies,
    packageManager: "pip"
  };
}

function parsePubspecYaml(content: string): PackageManagerData {
  const dependencies: Record<string, string> = {};
  const lines = content.split('\n');
  let inDependencies = false;
  let inDevDependencies = false;
  
  for (const line of lines) {
    const trimmed = line.trim();
    
    if (trimmed === 'dependencies:') {
      inDependencies = true;
      inDevDependencies = false;
      continue;
    } else if (trimmed === 'dev_dependencies:') {
      inDependencies = false;
      inDevDependencies = true;
      continue;
    } else if (trimmed && !trimmed.startsWith(' ') && trimmed.endsWith(':')) {
      inDependencies = false;
      inDevDependencies = false;
      continue;
    }
    
    if ((inDependencies || inDevDependencies) && trimmed) {
      const match = trimmed.match(/^(\s+)([^:]+):\s*(.+)?$/);
      if (match) {
        const name = match[2].trim();
        const version = match[3] ? match[3].trim() : "any";
        
        if (name) {
          dependencies[name] = version;
        }
      }
    }
  }
  
  return {
    dependencies,
    packageManager: "pub"
  };
}

function detectFileType(content: string, fileName: string): PackageManagerData {
  const lowerFileName = fileName.toLowerCase();
  
  if (lowerFileName.includes('package.json') || content.trim().startsWith('{')) {
    return parsePackageJson(content);
  } else if (lowerFileName.includes('requirements.txt') || lowerFileName.includes('.txt')) {
    return parseRequirementsTxt(content);
  } else if (lowerFileName.includes('pubspec.yaml') || lowerFileName.includes('pubspec.yml') || lowerFileName.includes('.yaml') || lowerFileName.includes('.yml')) {
    return parsePubspecYaml(content);
  } else {
    // Try to detect by content
    try {
      return parsePackageJson(content);
    } catch {
      try {
        return parseRequirementsTxt(content);
      } catch {
        return parsePubspecYaml(content);
      }
    }
  }
}

function cleanVersion(version: string): string {
  // Remove version specifiers and clean up
  return version
    .replace(/[\^~>=<]/g, "")
    .replace(/v/gi, "")
    .trim();
}

function compareVersions(current: string, latest: string): "up-to-date" | "outdated" | "error" {
  if (latest === "unknown" || current === "unknown") {
    return "error";
  }
  
  if (latest === "latest" || current === "latest") {
    return "up-to-date";
  }
  
  try {
    const cleanCurrent = cleanVersion(current);
    const cleanLatest = cleanVersion(latest);
    
    if (cleanCurrent === cleanLatest) {
      return "up-to-date";
    }
    
    // Handle special cases like "any", "*", etc.
    if (cleanCurrent === "any" || cleanCurrent === "*") {
      return "up-to-date";
    }
    
    // Split version parts and compare numerically
    const currentParts = cleanCurrent.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? part : num;
    });
    
    const latestParts = cleanLatest.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? part : num;
    });
    
    for (let i = 0; i < Math.max(currentParts.length, latestParts.length); i++) {
      const currentPart = currentParts[i] || 0;
      const latestPart = latestParts[i] || 0;
      
      if (typeof currentPart === 'string' && typeof latestPart === 'string') {
        // Compare strings lexicographically
        if (latestPart > currentPart) return "outdated";
        if (currentPart > latestPart) return "up-to-date";
      } else {
        // Compare numbers
        if (latestPart > currentPart) return "outdated";
        if (currentPart > latestPart) return "up-to-date";
      }
    }
    
    return "up-to-date";
  } catch (error) {
    console.error('Error comparing versions:', error);
    return "error";
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content, fileName } = body;
    
    if (!content) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      );
    }
    
    // Parse the content based on file type
    let packageData: PackageManagerData;
    try {
      packageData = detectFileType(content, fileName);
    } catch (error) {
      return NextResponse.json(
        { error: "Failed to parse file content" },
        { status: 400 }
      );
    }
    
    const packageNames = Object.keys(packageData.dependencies);
    
    if (packageNames.length === 0) {
      return NextResponse.json({
        packages: [],
        summary: {
          total: 0,
          upToDate: 0,
          outdated: 0,
          errors: 0
        }
      });
    }
    
    // Prepare packages for batch processing
    const packagesToCheck = packageNames.map(name => ({
      name,
      manager: packageData.packageManager
    }));
    
    // Get package information from native APIs
    const packageInfos = await PackageManagerService.getMultiplePackagesInfo(packagesToCheck);
    
    // Process results
    const packages: PackageInfo[] = packageInfos.map((info, index) => {
      const packageName = packageNames[index];
      const currentVersion = packageData.dependencies[packageName];
      const status = compareVersions(currentVersion, info.latestVersion);
      
      return {
        name: packageName,
        currentVersion,
        latestVersion: info.latestVersion,
        status,
        packageManager: packageData.packageManager,
        description: info.description,
        homepage: info.homepage
      };
    });
    
    const summary = {
      total: packages.length,
      upToDate: packages.filter(p => p.status === "up-to-date").length,
      outdated: packages.filter(p => p.status === "outdated").length,
      errors: packages.filter(p => p.status === "error").length
    };
    
    return NextResponse.json({
      packages,
      summary
    });
    
  } catch (error) {
    console.error("Error in analyze-packages API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}