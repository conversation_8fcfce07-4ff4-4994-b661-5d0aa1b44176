{"name": "pack_man", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.1", "@mdxeditor/editor": "^3.40.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reactuses/core": "^6.0.5", "@tanstack/react-query": "^5.84.2", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "input-otp": "^1.4.2", "lucide-react": "^0.539.0", "next": "15.4.6", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "react-syntax-highlighter": "^15.6.1", "recharts": "3.1.2", "sharp": "^0.34.3", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "uuid": "^11.1.0", "vaul": "^1.1.2", "z-ai-web-dev-sdk": "^0.0.10", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@tailwindcss/postcss": "4.1.11", "@types/node": "24.2.1", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "eslint": "9.33.0", "eslint-config-next": "15.4.6", "nodemon": "3.1.10", "tailwindcss": "4.1.11", "tw-animate-css": "^1.3.6", "typescript": "5.9.2"}}