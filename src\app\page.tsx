"use client";

import { useState, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Upload, FileText, Clipboard, CheckCircle, AlertCircle, Loader2, Smartphone, Monitor, Package, ExternalLink, TrendingUp, TrendingDown, AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PackageInfo {
  name: string;
  currentVersion: string;
  latestVersion: string;
  status: "up-to-date" | "outdated" | "error";
  packageManager: "npm" | "pip" | "pub";
  homepage?: string;
  description?: string;
}

interface AnalysisSummary {
  total: number;
  upToDate: number;
  outdated: number;
  errors: number;
}

export default function Home() {
  const [fileContent, setFileContent] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [results, setResults] = useState<PackageInfo[]>([]);
  const [summary, setSummary] = useState<AnalysisSummary | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setFileContent(content);
      };
      reader.readAsText(file);
    }
  }, []);

  const handleAnalyze = async () => {
    if (!fileContent.trim()) {
      setError("Por favor, forneça o conteúdo do arquivo");
      return;
    }

    setIsAnalyzing(true);
    setError(null);
    setResults([]);
    setSummary(null);
    setAnalysisProgress(0);

    try {
      const response = await fetch("/api/analyze-packages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: fileContent,
          fileName: selectedFile?.name || "unknown",
        }),
      });

      if (!response.ok) {
        throw new Error("Erro ao analisar pacotes");
      }

      const data = await response.json();
      setResults(data.packages || []);
      setSummary(data.summary || null);
      
      if (data.packages?.length === 0) {
        setError("Nenhum pacote encontrado no arquivo");
      } else {
        toast({
          title: "Análise concluída",
          description: `Encontrados ${data.packages?.length || 0} pacotes`,
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro desconhecido");
      toast({
        title: "Erro na análise",
        description: err instanceof Error ? err.message : "Erro desconhecido",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
    }
  };

  const getStatusColor = (status: PackageInfo["status"]) => {
    switch (status) {
      case "up-to-date":
        return "bg-green-100 text-green-800 border-green-200";
      case "outdated":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "error":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: PackageInfo["status"]) => {
    switch (status) {
      case "up-to-date":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "outdated":
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      case "error":
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      default:
        return null;
    }
  };

  const getPackageManagerIcon = (packageManager: PackageInfo["packageManager"]) => {
    switch (packageManager) {
      case "npm":
        return <Package className="w-4 h-4" />;
      case "pip":
        return <Package className="w-4 h-4" />;
      case "pub":
        return <Package className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const getPackageUrl = (pkg: PackageInfo) => {
    switch (pkg.packageManager) {
      case "npm":
        return `https://www.npmjs.com/package/${pkg.name}`;
      case "pip":
        return `https://pypi.org/project/${pkg.name}/`;
      case "pub":
        return `https://pub.dev/packages/${pkg.name}`;
      default:
        return pkg.homepage || "#";
    }
  };

  const upToDatePackages = results.filter(pkg => pkg.status === "up-to-date");
  const outdatedPackages = results.filter(pkg => pkg.status === "outdated");
  const errorPackages = results.filter(pkg => pkg.status === "error");

  const PackageCard = ({ pkg }: { pkg: PackageInfo }) => {
    const packageUrl = getPackageUrl(pkg);
    
    return (
      <div className="flex flex-col sm:flex-row sm:items-start gap-3 p-3 sm:p-4 border rounded-lg hover:shadow-md transition-shadow">
        <div className="flex items-start gap-3 flex-1">
          {getStatusIcon(pkg.status)}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-medium text-sm sm:text-base truncate">
                <a 
                  href={packageUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:text-primary transition-colors flex items-center gap-1"
                >
                  {pkg.name}
                  <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
                </a>
              </h3>
            </div>
            {pkg.description && (
              <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2 mb-2">
                {pkg.description}
              </p>
            )}
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 text-xs sm:text-sm">
              <span className="text-muted-foreground">
                Atual: <span className="font-medium">{pkg.currentVersion}</span>
              </span>
              {pkg.status === "outdated" && (
                <span className="text-yellow-600 font-medium flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  Latest: {pkg.latestVersion}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          <Badge className={`${getStatusColor(pkg.status)} text-xs sm:text-sm`}>
            {pkg.status === "up-to-date" && "Atualizado"}
            {pkg.status === "outdated" && "Desatualizado"}
            {pkg.status === "error" && "Erro"}
          </Badge>
          <Badge variant="outline" className="text-xs sm:text-sm flex items-center gap-1">
            {getPackageManagerIcon(pkg.packageManager)}
            {pkg.packageManager === "npm" && "NPM"}
            {pkg.packageManager === "pip" && "PIP"}
            {pkg.packageManager === "pub" && "Pub"}
          </Badge>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="max-w-6xl mx-auto space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="text-center space-y-3 sm:space-y-4">
          <div className="flex items-center justify-center gap-2 sm:gap-3">
            <div className="relative w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12">
              <Package className="w-full h-full text-primary" />
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tight">
              Package Version Checker
            </h1>
          </div>
          <p className="text-sm sm:text-base md:text-lg text-muted-foreground max-w-2xl mx-auto px-4">
            Verifique as versões mais recentes dos seus pacotes e mantenha seu projeto atualizado
          </p>
          <div className="flex items-center justify-center gap-4 sm:gap-6 text-xs sm:text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Smartphone className="w-4 h-4" />
              <span>Mobile Friendly</span>
            </div>
            <div className="flex items-center gap-1">
              <Monitor className="w-4 h-4" />
              <span>Desktop Optimized</span>
            </div>
          </div>
        </div>

        {/* Main Card */}
        <Card className="border-0 shadow-lg sm:shadow-xl">
          <CardHeader className="space-y-3 sm:space-y-4 pb-4 sm:pb-6">
            <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:gap-3 gap-2 text-lg sm:text-xl">
              <div className="flex items-center gap-2">
                <FileText className="w-5 h-5 sm:w-6 sm:h-6" />
                <span>Analisar Dependências</span>
              </div>
            </CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Faça upload de arquivos de dependências ou cole o conteúdo manualmente
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 sm:space-y-6">
            <Tabs defaultValue="upload" className="w-full">
              <TabsList className="grid w-full grid-cols-2 h-10 sm:h-12">
                <TabsTrigger value="upload" className="flex items-center gap-2 text-xs sm:text-sm py-2 sm:py-3">
                  <Upload className="w-4 h-4" />
                  <span className="hidden sm:inline">Upload de Arquivo</span>
                  <span className="sm:hidden">Upload</span>
                </TabsTrigger>
                <TabsTrigger value="manual" className="flex items-center gap-2 text-xs sm:text-sm py-2 sm:py-3">
                  <Clipboard className="w-4 h-4" />
                  <span className="hidden sm:inline">Colar Manualmente</span>
                  <span className="sm:hidden">Colar</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="space-y-3 sm:space-y-4 mt-4 sm:mt-6">
                <div className="space-y-2">
                  <Label htmlFor="file-upload" className="text-sm sm:text-base font-medium">
                    Selecione o arquivo de dependências
                  </Label>
                  <div className="relative">
                    <Input
                      id="file-upload"
                      type="file"
                      accept=".json,.txt,.yaml,.yml"
                      onChange={handleFileUpload}
                      className="cursor-pointer text-sm sm:text-base"
                    />
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <span className="text-muted-foreground text-sm sm:text-base">
                        {selectedFile ? selectedFile.name : "Clique para selecionar arquivo"}
                      </span>
                    </div>
                  </div>
                  <p className="text-xs sm:text-sm text-muted-foreground">
                    Formatos suportados: package.json, requirements.txt, pubspec.yaml
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="manual" className="space-y-3 sm:space-y-4 mt-4 sm:mt-6">
                <div className="space-y-2">
                  <Label htmlFor="manual-input" className="text-sm sm:text-base font-medium">
                    Cole o conteúdo do arquivo
                  </Label>
                  <Textarea
                    id="manual-input"
                    placeholder={`{
  "name": "my-project",
  "dependencies": {
    "react": "^18.0.0",
    "next": "13.0.0"
  }
}`}
                    value={fileContent}
                    onChange={(e) => setFileContent(e.target.value)}
                    rows={8}
                    className="font-mono text-xs sm:text-sm min-h-[120px] sm:min-h-[150px]"
                  />
                </div>
              </TabsContent>
            </Tabs>

            {selectedFile && (
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border">
                <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground" />
                <span className="text-sm sm:text-base font-medium truncate">{selectedFile.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {(selectedFile.size / 1024).toFixed(1)} KB
                </Badge>
              </div>
            )}

            {isAnalyzing && (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm sm:text-base">
                  <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                  <span>Analisando pacotes...</span>
                </div>
                <Progress value={analysisProgress} className="h-2" />
              </div>
            )}

            <Button
              onClick={handleAnalyze}
              disabled={isAnalyzing || !fileContent.trim()}
              className="w-full h-12 sm:h-14 text-base sm:text-lg font-medium"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 animate-spin" />
                  Analisando...
                </>
              ) : (
                "Analisar Pacotes"
              )}
            </Button>

            {error && (
              <Alert variant="destructive" className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                <AlertDescription className="text-sm sm:text-base">{error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Results */}
        {results.length > 0 && summary && (
          <Card className="border-0 shadow-lg sm:shadow-xl">
            <CardHeader className="space-y-3 sm:space-y-4 pb-4 sm:pb-6">
              <CardTitle className="text-lg sm:text-xl flex items-center gap-2">
                <Package className="w-5 h-5 sm:w-6 sm:h-6" />
                Resultados da Análise
              </CardTitle>
              
              {/* Summary Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-2xl sm:text-3xl font-bold text-blue-600">{summary.total}</div>
                  <div className="text-xs sm:text-sm text-blue-600 font-medium">Total</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-2xl sm:text-3xl font-bold text-green-600">{summary.upToDate}</div>
                  <div className="text-xs sm:text-sm text-green-600 font-medium">Atualizados</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="text-2xl sm:text-3xl font-bold text-yellow-600">{summary.outdated}</div>
                  <div className="text-xs sm:text-sm text-yellow-600 font-medium">Desatualizados</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                  <div className="text-2xl sm:text-3xl font-bold text-red-600">{summary.errors}</div>
                  <div className="text-xs sm:text-sm text-red-600 font-medium">Erros</div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Accordion type="multiple" defaultValue={["up-to-date", "outdated"]} className="w-full">
                {/* Up-to-date Packages */}
                <AccordionItem value="up-to-date">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span className="font-medium">Pacotes Atualizados ({upToDatePackages.length})</span>
                      <Badge variant="secondary" className="ml-auto">
                        {upToDatePackages.length}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3 sm:space-y-4">
                      {upToDatePackages.length > 0 ? (
                        upToDatePackages.map((pkg, index) => (
                          <PackageCard key={index} pkg={pkg} />
                        ))
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          Nenhum pacote atualizado encontrado
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                {/* Outdated Packages */}
                <AccordionItem value="outdated">
                  <AccordionTrigger className="hover:no-underline">
                    <div className="flex items-center gap-3">
                      <AlertCircle className="w-5 h-5 text-yellow-600" />
                      <span className="font-medium">Pacotes Desatualizados ({outdatedPackages.length})</span>
                      <Badge variant="destructive" className="ml-auto">
                        {outdatedPackages.length}
                      </Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-3 sm:space-y-4">
                      {outdatedPackages.length > 0 ? (
                        outdatedPackages.map((pkg, index) => (
                          <PackageCard key={index} pkg={pkg} />
                        ))
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          Nenhum pacote desatualizado encontrado
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>

                {/* Error Packages */}
                {errorPackages.length > 0 && (
                  <AccordionItem value="error">
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center gap-3">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                        <span className="font-medium">Pacotes com Erro ({errorPackages.length})</span>
                        <Badge variant="destructive" className="ml-auto">
                          {errorPackages.length}
                        </Badge>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3 sm:space-y-4">
                        {errorPackages.map((pkg, index) => (
                          <PackageCard key={index} pkg={pkg} />
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                )}
              </Accordion>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}