"use client";

import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { PackageInfo, AnalysisSummary } from "@/types/package";

interface UsePackageAnalysisProps {
  onAnalysisComplete: (results: PackageInfo[], summary: AnalysisSummary) => void;
  onError: (error: string) => void;
}

export function usePackageAnalysis({ onAnalysisComplete, onError }: UsePackageAnalysisProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const { toast } = useToast();

  const analyzePackages = async (content: string, fileName: string) => {
    if (!content.trim()) {
      onError("Por favor, forneça o conteúdo do arquivo");
      return;
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch("/api/analyze-packages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content,
          fileName,
        }),
      });

      clearInterval(progressInterval);
      setAnalysisProgress(100);

      if (!response.ok) {
        throw new Error("Erro ao analisar pacotes");
      }

      const data = await response.json();
      
      if (data.packages?.length === 0) {
        onError("Nenhum pacote encontrado no arquivo");
      } else {
        onAnalysisComplete(data.packages || [], data.summary || null);
        toast({
          title: "Análise concluída",
          description: `Encontrados ${data.packages?.length || 0} pacotes`,
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erro desconhecido";
      onError(errorMessage);
      toast({
        title: "Erro na análise",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setTimeout(() => {
        setIsAnalyzing(false);
        setAnalysisProgress(0);
      }, 500);
    }
  };

  return {
    isAnalyzing,
    analysisProgress,
    analyzePackages,
  };
}