"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Upload,
  Clipboard,
  Loader2,
  FileText,
  AlertCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useFileUpload } from "@/hooks/use-file-upload";
import { formatFileSize } from "@/lib/package-utils";

interface FileUploadSectionProps {
  fileContent: string;
  selectedFile: File | null;
  onFileContentChange: (content: string) => void;
  onFileSelect: (file: File | null) => void;
  onAnalyze: () => void;
  isAnalyzing: boolean;
  analysisProgress: number;
  error: string | null;
}

export function FileUploadSection({
  fileContent,
  selectedFile,
  onFileContentChange,
  onFileSelect,
  onAnalyze,
  isAnalyzing,
  analysisProgress,
  error,
}: FileUploadSectionProps) {
  const { handleFileUpload } = useFileUpload({
    onFileContentChange,
    onFileSelect,
  });

  return (
    <div className="space-y-4 sm:space-y-6">
      <Tabs defaultValue="upload" className="w-full">
        <TabsList className="grid w-full grid-cols-2 h-10 sm:h-12">
          <TabsTrigger
            value="upload"
            className="flex items-center gap-2 text-xs sm:text-sm py-2 sm:py-3"
          >
            <Upload className="w-4 h-4" />
            <span className="hidden sm:inline">Upload de Arquivo</span>
            <span className="sm:hidden">Upload</span>
          </TabsTrigger>
          <TabsTrigger
            value="manual"
            className="flex items-center gap-2 text-xs sm:text-sm py-2 sm:py-3"
          >
            <Clipboard className="w-4 h-4" />
            <span className="hidden sm:inline">Colar Manualmente</span>
            <span className="sm:hidden">Colar</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="upload"
          className="space-y-3 sm:space-y-4 mt-4 sm:mt-6"
        >
          <div className="space-y-2">
            <Label
              htmlFor="file-upload"
              className="text-sm sm:text-base font-medium"
            >
              Selecione o arquivo de dependências
            </Label>
            <Label
              htmlFor="file-upload"
              className="relative flex items-center justify-start w-full h-10 px-3 py-2 text-sm border rounded-md cursor-pointer bg-background text-muted-foreground"
            >
              <span className="truncate">
                {selectedFile
                  ? selectedFile.name
                  : "Clique para selecionar um arquivo"}
              </span>
              <Input
                id="file-upload"
                type="file"
                accept=".json,.txt,.yaml,.yml"
                onChange={handleFileUpload}
                className="sr-only"
              />
            </Label>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Formatos suportados: package.json, requirements.txt, pubspec.yaml
            </p>
          </div>
        </TabsContent>

        <TabsContent
          value="manual"
          className="space-y-3 sm:space-y-4 mt-4 sm:mt-6"
        >
          <div className="space-y-2">
            <Label
              htmlFor="manual-input"
              className="text-sm sm:text-base font-medium"
            >
              Cole o conteúdo do arquivo
            </Label>
            <Textarea
              id="manual-input"
              placeholder={`{
  "name": "my-project",
  "dependencies": {
    "react": "^18.0.0",
    "next": "13.0.0"
  }
}`}
              value={fileContent}
              onChange={(e) => onFileContentChange(e.target.value)}
              rows={8}
              className="font-mono text-xs sm:text-sm min-h-[120px] sm:min-h-[150px]"
            />
          </div>
        </TabsContent>
      </Tabs>

      {selectedFile && (
        <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border">
          <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-muted-foreground" />
          <span className="text-sm sm:text-base font-medium truncate">
            {selectedFile.name}
          </span>
          <Badge variant="secondary" className="text-xs">
            {formatFileSize(selectedFile.size)}
          </Badge>
        </div>
      )}

      {isAnalyzing && (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm sm:text-base">
            <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
            <span>Analisando pacotes...</span>
          </div>
          <Progress value={analysisProgress} className="h-2" />
        </div>
      )}

      <Button
        onClick={onAnalyze}
        disabled={isAnalyzing || !fileContent.trim()}
        className="w-full h-12 sm:h-14 text-base sm:text-lg font-medium"
      >
        {isAnalyzing ? (
          <>
            <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 animate-spin" />
            Analisando...
          </>
        ) : (
          "Analisar Pacotes"
        )}
      </Button>

      {error && (
        <Alert variant="destructive" className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5" />
          <AlertDescription className="text-sm sm:text-base">
            {error}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
