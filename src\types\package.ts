export interface PackageInfo {
  name: string;
  currentVersion: string;
  latestVersion: string;
  status: "up-to-date" | "outdated" | "error";
  packageManager: "npm" | "pip" | "pub";
  homepage?: string;
  description?: string;
}

export interface AnalysisSummary {
  total: number;
  upToDate: number;
  outdated: number;
  errors: number;
}

export interface PackageManagerData {
  dependencies: Record<string, string>;
  devDependencies?: Record<string, string>;
  packageManager: "npm" | "pip" | "pub";
}

export interface PackageVersionInfo {
  name: string;
  latestVersion: string;
  description?: string;
  homepage?: string;
  error?: string;
}

export type PackageStatus = PackageInfo["status"];
export type PackageManagerType = PackageInfo["packageManager"];