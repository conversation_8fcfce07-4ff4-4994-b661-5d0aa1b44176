"use client";

import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, AlertTriangle } from "lucide-react";
import { PackageInfo } from "@/types/package";
import { PackageCard } from "./PackageCard";

interface PackageResultsProps {
  results: PackageInfo[];
}

export function PackageResults({ results }: PackageResultsProps) {
  const upToDatePackages = results.filter(pkg => pkg.status === "up-to-date");
  const outdatedPackages = results.filter(pkg => pkg.status === "outdated");
  const errorPackages = results.filter(pkg => pkg.status === "error");

  return (
    <Accordion type="multiple" defaultValue={["up-to-date", "outdated"]} className="w-full">
      {/* Up-to-date Packages */}
      <AccordionItem value="up-to-date">
        <AccordionTrigger className="hover:no-underline">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="font-medium">Pacotes Atualizados ({upToDatePackages.length})</span>
            <Badge variant="secondary" className="ml-auto">
              {upToDatePackages.length}
            </Badge>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-3 sm:space-y-4">
            {upToDatePackages.length > 0 ? (
              upToDatePackages.map((pkg, index) => (
                <PackageCard key={index} pkg={pkg} />
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum pacote atualizado encontrado
              </div>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>

      {/* Outdated Packages */}
      <AccordionItem value="outdated">
        <AccordionTrigger className="hover:no-underline">
          <div className="flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-yellow-600" />
            <span className="font-medium">Pacotes Desatualizados ({outdatedPackages.length})</span>
            <Badge variant="destructive" className="ml-auto">
              {outdatedPackages.length}
            </Badge>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-3 sm:space-y-4">
            {outdatedPackages.length > 0 ? (
              outdatedPackages.map((pkg, index) => (
                <PackageCard key={index} pkg={pkg} />
              ))
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Nenhum pacote desatualizado encontrado
              </div>
            )}
          </div>
        </AccordionContent>
      </AccordionItem>

      {/* Error Packages */}
      {errorPackages.length > 0 && (
        <AccordionItem value="error">
          <AccordionTrigger className="hover:no-underline">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <span className="font-medium">Pacotes com Erro ({errorPackages.length})</span>
              <Badge variant="destructive" className="ml-auto">
                {errorPackages.length}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-3 sm:space-y-4">
              {errorPackages.map((pkg, index) => (
                <PackageCard key={index} pkg={pkg} />
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      )}
    </Accordion>
  );
}