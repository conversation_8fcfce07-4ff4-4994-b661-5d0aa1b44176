"use client";

import { CheckCircle, AlertCircle, AlertTriangle, Package } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PackageInfo } from "@/types/package";
import { getPackageUrl, getStatusColor } from "@/lib/package-utils";
import { ExternalLink, TrendingUp } from "lucide-react";

interface PackageCardProps {
  pkg: PackageInfo;
}

export function PackageCard({ pkg }: PackageCardProps) {
  const packageUrl = getPackageUrl(pkg);
  
  const getStatusIcon = () => {
    switch (pkg.status) {
      case "up-to-date":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "outdated":
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      case "error":
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      default:
        return null;
    }
  };

  const getPackageManagerIcon = () => {
    return <Package className="w-4 h-4" />;
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-start gap-3 p-3 sm:p-4 border rounded-lg hover:shadow-md transition-shadow">
      <div className="flex items-start gap-3 flex-1">
        {getStatusIcon()}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-medium text-sm sm:text-base truncate">
              <a 
                href={packageUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="hover:text-primary transition-colors flex items-center gap-1"
              >
                {pkg.name}
                <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
              </a>
            </h3>
          </div>
          {pkg.description && (
            <p className="text-xs sm:text-sm text-muted-foreground mt-1 line-clamp-2 mb-2">
              {pkg.description}
            </p>
          )}
          <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 text-xs sm:text-sm">
            <span className="text-muted-foreground">
              Atual: <span className="font-medium">{pkg.currentVersion}</span>
            </span>
            {pkg.status === "outdated" && (
              <span className="text-yellow-600 font-medium flex items-center gap-1">
                <TrendingUp className="w-3 h-3" />
                Latest: {pkg.latestVersion}
              </span>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2 flex-shrink-0">
        <Badge className={`${getStatusColor(pkg.status)} text-xs sm:text-sm`}>
          {pkg.status === "up-to-date" && "Atualizado"}
          {pkg.status === "outdated" && "Desatualizado"}
          {pkg.status === "error" && "Erro"}
        </Badge>
        <Badge variant="outline" className="text-xs sm:text-sm flex items-center gap-1">
          {getPackageManagerIcon()}
          {pkg.packageManager === "npm" && "NPM"}
          {pkg.packageManager === "pip" && "PIP"}
          {pkg.packageManager === "pub" && "Pub"}
        </Badge>
      </div>
    </div>
  );
}