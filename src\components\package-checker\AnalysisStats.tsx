"use client";

import { Card, CardContent } from "@/components/ui/card";
import { AnalysisSummary } from "@/types/package";

interface AnalysisStatsProps {
  summary: AnalysisSummary;
}

export function AnalysisStats({ summary }: AnalysisStatsProps) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
      <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="text-2xl sm:text-3xl font-bold text-blue-600">{summary.total}</div>
        <div className="text-xs sm:text-sm text-blue-600 font-medium">Total</div>
      </div>
      <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
        <div className="text-2xl sm:text-3xl font-bold text-green-600">{summary.upToDate}</div>
        <div className="text-xs sm:text-sm text-green-600 font-medium">Atualizados</div>
      </div>
      <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="text-2xl sm:text-3xl font-bold text-yellow-600">{summary.outdated}</div>
        <div className="text-xs sm:text-sm text-yellow-600 font-medium">Desatualizados</div>
      </div>
      <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
        <div className="text-2xl sm:text-3xl font-bold text-red-600">{summary.errors}</div>
        <div className="text-xs sm:text-sm text-red-600 font-medium">Erros</div>
      </div>
    </div>
  );
}